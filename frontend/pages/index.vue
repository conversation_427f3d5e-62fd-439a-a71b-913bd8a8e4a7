<template>
  <div class="home-page">
    <!-- 欢迎区域 -->
    <section class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎使用个人记账应用</h1>
        <p class="welcome-description">
          轻松管理您的收支，掌控财务状况，实现理财目标
        </p>
        <div class="welcome-actions" v-if="!isLoggedIn">
          <NuxtLink to="/auth/register" class="btn btn-primary">
            开始记账
          </NuxtLink>
          <NuxtLink to="/auth/login" class="btn btn-secondary">
            立即登录
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- 功能特色 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">💰</div>
            <h3 class="feature-title">收支记录</h3>
            <p class="feature-description">
              快速记录每笔收入和支出，支持多种分类管理
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3 class="feature-title">统计分析</h3>
            <p class="feature-description">
              直观的图表展示，帮您分析消费习惯和财务趋势
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3 class="feature-title">预算管理</h3>
            <p class="feature-description">
              设置预算目标，实时跟踪支出，避免超支
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3 class="feature-title">多端同步</h3>
            <p class="feature-description">
              支持手机、平板、电脑多设备访问，数据实时同步
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户已登录时显示快速操作 -->
    <section v-if="isLoggedIn" class="quick-actions-section">
      <div class="container">
        <h2 class="section-title">快速操作</h2>
        <div class="quick-actions">
          <NuxtLink to="/records/new" class="quick-action-card">
            <div class="action-icon">➕</div>
            <h3 class="action-title">添加记录</h3>
            <p class="action-description">记录新的收入或支出</p>
          </NuxtLink>
          <NuxtLink to="/records" class="quick-action-card">
            <div class="action-icon">📋</div>
            <h3 class="action-title">查看记录</h3>
            <p class="action-description">浏览所有财务记录</p>
          </NuxtLink>
          <NuxtLink to="/statistics" class="quick-action-card">
            <div class="action-icon">📈</div>
            <h3 class="action-title">统计报表</h3>
            <p class="action-description">查看财务分析报告</p>
          </NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
// 设置页面元信息
useHead({
  title: '首页 - 个人记账应用',
  meta: [
    { name: 'description', content: '个人记账应用首页，轻松管理您的财务' }
  ]
})

// 获取用户认证状态
const { isLoggedIn } = useAuth()
</script>

<style scoped>
.home-page {
  min-height: calc(100vh - 200px);
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.welcome-description {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.welcome-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.features-section {
  padding: 4rem 0;
  background: #f9fafb;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 3rem;
  color: #1f2937;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.feature-description {
  color: #6b7280;
  line-height: 1.6;
}

.quick-actions-section {
  padding: 4rem 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.quick-action-card {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  text-align: center;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.action-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #1f2937;
}

.action-description {
  color: #6b7280;
  font-size: 0.875rem;
}

@media (max-width: 768px) {
  .welcome-title {
    font-size: 2rem;
  }

  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
