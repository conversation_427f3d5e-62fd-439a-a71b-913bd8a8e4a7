<template>
  <div class="login-page">
    <div class="login-form">
      <h2 class="form-title">登录账户</h2>
      <p class="form-subtitle">欢迎回来，请登录您的账户</p>
      
      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <UiInput
            v-model="form.email"
            type="email"
            label="邮箱地址"
            placeholder="请输入邮箱地址"
            required
            :error="errors.email"
          />
        </div>
        
        <div class="form-group">
          <UiInput
            v-model="form.password"
            type="password"
            label="密码"
            placeholder="请输入密码"
            required
            :error="errors.password"
          />
        </div>
        
        <div class="form-options">
          <label class="checkbox-label">
            <input
              v-model="form.rememberMe"
              type="checkbox"
              class="checkbox"
            >
            <span class="checkbox-text">记住我</span>
          </label>
          <NuxtLink to="/auth/forgot-password" class="forgot-link">
            忘记密码？
          </NuxtLink>
        </div>
        
        <UiButton
          type="submit"
          :disabled="isLoading"
          class="submit-btn"
        >
          {{ isLoading ? '登录中...' : '登录' }}
        </UiButton>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </form>
      
      <div class="form-footer">
        <p class="register-prompt">
          还没有账户？
          <NuxtLink to="/auth/register" class="register-link">
            立即注册
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 使用认证布局
definePageMeta({
  layout: 'auth'
})

// 设置页面元信息
useHead({
  title: '登录 - 个人记账应用'
})

const { login, isLoading } = useAuth()
const router = useRouter()

// 表单数据
const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

// 错误信息
const errors = reactive({
  email: '',
  password: ''
})

const errorMessage = ref('')

// 表单验证
const validateForm = () => {
  // 重置错误
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })
  errorMessage.value = ''

  let isValid = true

  // 邮箱验证
  if (!form.email) {
    errors.email = '请输入邮箱地址'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
    isValid = false
  }

  // 密码验证
  if (!form.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (form.password.length < 6) {
    errors.password = '密码至少需要6个字符'
    isValid = false
  }

  return isValid
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  const result = await login({
    email: form.email,
    password: form.password
  })

  if (!result.success) {
    errorMessage.value = result.message || '登录失败，请重试'
  }
}

// 如果已经登录，重定向到首页
const { isLoggedIn } = useAuth()
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    router.push('/')
  }
}, { immediate: true })
</script>

<style scoped>
.login-page {
  width: 100%;
}

.login-form {
  width: 100%;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.form-subtitle {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  margin-right: 0.5rem;
}

.checkbox-text {
  font-size: 0.875rem;
  color: #374151;
}

.forgot-link {
  font-size: 0.875rem;
  color: #3b82f6;
  text-decoration: none;
}

.forgot-link:hover {
  text-decoration: underline;
}

.submit-btn {
  width: 100%;
  margin-bottom: 1rem;
}

.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.form-footer {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.register-prompt {
  color: #6b7280;
  font-size: 0.875rem;
}

.register-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.register-link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .form-options {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }
}
</style>
