<template>
  <div class="form-group">
    <label v-if="label" :for="inputId" class="form-label">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <input :id="inputId" v-model="inputValue" :type="type" :placeholder="placeholder" :required="required"
      :disabled="disabled" :class="inputClasses" @blur="$emit('blur', $event)" @focus="$emit('focus', $event)">
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    <div v-if="hint" class="hint-message">
      {{ hint }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => ['text', 'email', 'password', 'number', 'tel', 'url', 'date'].includes(value)
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  hint: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus'])

const inputId = computed(() => `input-${Math.random().toString(36).substr(2, 9)}`)

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const inputClasses = computed(() => {
  const baseClasses = 'form-input'
  const errorClasses = props.error ? 'border-red-500 focus:border-red-500' : ''
  const disabledClasses = props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''

  return [baseClasses, errorClasses, disabledClasses].filter(Boolean).join(' ')
})
</script>

<style scoped>
.error-message {
  @apply text-red-500 text-sm mt-1;
}

.hint-message {
  @apply text-gray-500 text-sm mt-1;
}
</style>
