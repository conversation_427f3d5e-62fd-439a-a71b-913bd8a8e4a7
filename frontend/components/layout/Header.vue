<template>
  <header class="header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo">
          <NuxtLink to="/" class="logo-link">
            <h1 class="logo-text">记账本</h1>
          </NuxtLink>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav" :class="{ 'nav-open': isMenuOpen }">
          <NuxtLink to="/" class="nav-link" @click="closeMenu">
            首页
          </NuxtLink>
          <NuxtLink to="/records" class="nav-link" @click="closeMenu">
            记录管理
          </NuxtLink>
          <NuxtLink to="/statistics" class="nav-link" @click="closeMenu">
            统计分析
          </NuxtLink>
          <NuxtLink to="/settings" class="nav-link" @click="closeMenu">
            设置
          </NuxtLink>
        </nav>

        <!-- 用户菜单 -->
        <div class="user-menu">
          <template v-if="isLoggedIn">
            <div class="user-info">
              <span class="user-name">{{ userName }}</span>
              <button @click="logout" class="logout-btn">
                退出
              </button>
            </div>
          </template>
          <template v-else>
            <NuxtLink to="/auth/login" class="btn btn-primary">
              登录
            </NuxtLink>
          </template>
        </div>

        <!-- 移动端菜单按钮 -->
        <button 
          class="menu-toggle"
          @click="toggleMenu"
          aria-label="切换菜单"
        >
          <span class="menu-icon"></span>
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
const isMenuOpen = ref(false)

// 这里应该从状态管理中获取用户信息
const isLoggedIn = ref(false)
const userName = ref('用户名')

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

const logout = () => {
  // 实现登出逻辑
  console.log('用户登出')
}

// 监听路由变化，关闭移动端菜单
const route = useRoute()
watch(() => route.path, () => {
  closeMenu()
})
</script>

<style scoped>
.header {
  @apply bg-white shadow-sm border-b border-gray-200;
}

.header-content {
  @apply flex items-center justify-between py-4;
}

.logo-link {
  @apply text-decoration-none;
}

.logo-text {
  @apply text-xl font-bold text-gray-800;
}

.nav {
  @apply hidden md:flex space-x-6;
}

.nav-open {
  @apply flex flex-col absolute top-full left-0 right-0 bg-white shadow-lg p-4 md:relative md:flex-row md:shadow-none md:p-0;
}

.nav-link {
  @apply text-gray-600 hover:text-gray-800 font-medium transition-colors;
}

.user-menu {
  @apply flex items-center space-x-4;
}

.user-info {
  @apply flex items-center space-x-2;
}

.user-name {
  @apply text-gray-700 font-medium;
}

.logout-btn {
  @apply text-gray-500 hover:text-gray-700 text-sm;
}

.menu-toggle {
  @apply md:hidden flex flex-col justify-center items-center w-6 h-6;
}

.menu-icon {
  @apply block w-5 h-0.5 bg-gray-600 transition-all;
}

.menu-icon::before,
.menu-icon::after {
  @apply content-[''] block w-5 h-0.5 bg-gray-600 transition-all;
}

.menu-icon::before {
  @apply transform -translate-y-1.5;
}

.menu-icon::after {
  @apply transform translate-y-1;
}
</style>
