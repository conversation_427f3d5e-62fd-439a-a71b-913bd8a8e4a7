<template>
  <form @submit.prevent="handleSubmit" class="record-form">
    <div class="form-row">
      <div class="form-group">
        <label class="form-label">类型</label>
        <div class="radio-group">
          <label class="radio-item">
            <input v-model="form.type" type="radio" value="income" class="radio-input">
            <span class="radio-label income">收入</span>
          </label>
          <label class="radio-item">
            <input v-model="form.type" type="radio" value="expense" class="radio-input">
            <span class="radio-label expense">支出</span>
          </label>
        </div>
      </div>
    </div>

    <div class="form-row">
      <UiInput v-model="form.amount" type="number" label="金额" placeholder="请输入金额" required :error="errors.amount" />
    </div>

    <div class="form-row">
      <div class="form-group">
        <label class="form-label">分类</label>
        <select v-model="form.category" class="form-select" required>
          <option value="">请选择分类</option>
          <option v-for="category in filteredCategories" :key="category.id" :value="category.name">
            {{ category.name }}
          </option>
        </select>
      </div>
    </div>

    <div class="form-row">
      <UiInput v-model="form.description" label="描述" placeholder="请输入描述（可选）" />
    </div>

    <div class="form-row">
      <UiInput v-model="form.recordDate" type="date" label="日期" required :error="errors.recordDate" />
    </div>

    <div class="form-actions">
      <UiButton type="button" variant="secondary" @click="$emit('cancel')">
        取消
      </UiButton>
      <UiButton type="submit" :disabled="isSubmitting">
        {{ isSubmitting ? '保存中...' : '保存' }}
      </UiButton>
    </div>
  </form>
</template>

<script setup>
const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'cancel'])

// 表单数据
const form = reactive({
  type: 'expense',
  amount: '',
  category: '',
  description: '',
  recordDate: new Date().toISOString().split('T')[0],
  ...props.initialData
})

// 表单验证错误
const errors = reactive({
  amount: '',
  recordDate: ''
})

// 提交状态
const isSubmitting = ref(false)

// 模拟分类数据（实际应该从 API 获取）
const categories = ref([
  { id: '1', name: '餐饮', type: 'expense' },
  { id: '2', name: '交通', type: 'expense' },
  { id: '3', name: '购物', type: 'expense' },
  { id: '4', name: '工资', type: 'income' },
  { id: '5', name: '奖金', type: 'income' },
  { id: '6', name: '投资', type: 'income' }
])

// 根据类型过滤分类
const filteredCategories = computed(() => {
  return categories.value.filter(cat => cat.type === form.type)
})

// 监听类型变化，重置分类
watch(() => form.type, () => {
  form.category = ''
})

// 表单验证
const validateForm = () => {
  // 重置错误
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  if (!form.amount || parseFloat(form.amount) <= 0) {
    errors.amount = '请输入有效的金额'
    isValid = false
  }

  if (!form.recordDate) {
    errors.recordDate = '请选择日期'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    // 这里应该调用 API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟 API 调用
    emit('submit', { ...form })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.record-form {
  @apply space-y-4;
}

.form-row {
  @apply w-full;
}

.radio-group {
  @apply flex space-x-4;
}

.radio-item {
  @apply flex items-center cursor-pointer;
}

.radio-input {
  @apply sr-only;
}

.radio-label {
  @apply px-4 py-2 rounded-lg border-2 transition-all;
}

.radio-input:checked+.radio-label.income {
  @apply bg-green-100 border-green-500 text-green-700;
}

.radio-input:checked+.radio-label.expense {
  @apply bg-red-100 border-red-500 text-red-700;
}

.radio-input:not(:checked)+.radio-label {
  @apply bg-gray-100 border-gray-300 text-gray-600;
}

.form-select {
  @apply form-input;
}

.form-actions {
  @apply flex justify-end space-x-3 pt-4;
}
</style>
