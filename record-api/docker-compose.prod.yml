version: '3.8'

services:
  record-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # 生产环境配置
      - RECORD_SERVER_PORT=8080
      - RECORD_SERVER_MODE=release
      
      # 数据库配置 (Supabase)
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_PORT=${DATABASE_PORT:-6543}
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_NAME=${DATABASE_NAME:-postgres}
      - DATABASE_SSLMODE=require
      
      # JWT 配置
      - RECORD_JWT_SECRET=${RECORD_JWT_SECRET}
      - RECORD_JWT_EXPIRETIME=${RECORD_JWT_EXPIRETIME:-24}
    volumes:
      - ./logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
