version: '3.8'

services:
  # Go 后端应用
  record-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # 服务器配置
      - RECORD_SERVER_PORT=8080
      - RECORD_SERVER_MODE=debug
      
      # 数据库配置 (使用 Supabase)
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_PORT=${DATABASE_PORT:-6543}
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_NAME=${DATABASE_NAME:-postgres}
      - DATABASE_SSLMODE=${DATABASE_SSLMODE:-require}
      
      # JWT 配置
      - RECORD_JWT_SECRET=${RECORD_JWT_SECRET:-your-jwt-secret-key}
      - RECORD_JWT_EXPIRETIME=${RECORD_JWT_EXPIRETIME:-24}
    volumes:
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载配置文件（可选）
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：本地 PostgreSQL 数据库（如果不使用 Supabase）
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     - POSTGRES_DB=record
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=postgres
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./database:/docker-entrypoint-initdb.d
  #   restart: unless-stopped

# volumes:
#   postgres_data:
