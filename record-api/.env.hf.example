# Hugging Face Spaces 环境变量配置示例
# 这些变量需要在 Hugging Face Spaces 的设置页面中配置

# 数据库配置 (Supabase)
DATABASE_HOST=aws-0-ap-southeast-1.pooler.supabase.com
DATABASE_PORT=6543
DATABASE_USER=postgres.your-project-ref
DATABASE_PASSWORD=your-supabase-password
DATABASE_NAME=postgres
DATABASE_SSLMODE=require

# 服务器配置
RECORD_SERVER_PORT=7860
RECORD_SERVER_MODE=release

# JWT 配置
RECORD_JWT_SECRET=your-production-jwt-secret-key-make-it-strong
RECORD_JWT_EXPIRETIME=24

# 注意事项：
# 1. 在 Hugging Face Spaces 中，应用必须监听 7860 端口
# 2. 所有敏感信息（如密码、密钥）都应该通过环境变量配置
# 3. 不要将真实的密码和密钥提交到代码仓库中
# 4. 建议在生产环境中使用强密码和随机生成的 JWT 密钥
