package database

import (
	"fmt"
	"log"

	"github.com/sakura/record-api/config"
	"github.com/sakura/record-api/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库连接
func InitDB() error {
	// 获取数据库配置
	dbConfig := config.GetConfig().Database

	// 构建连接DSN
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=Asia/Shanghai",
		dbConfig.Host, dbConfig.Port, dbConfig.User, dbConfig.Password, dbConfig.DBName, dbConfig.SSLMode,
	)

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 开发模式下使用更详细的日志
	if config.GetConfig().Server.Mode == "debug" {
		gormConfig.Logger = logger.Default.LogMode(logger.Info)
	}

	// 连接数据库
	var err error
	DB, err = gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return err
	}

	log.Println("数据库连接成功")

	// 自动迁移表结构
	err = migrateDB()
	if err != nil {
		return fmt.Errorf("自动迁移表结构失败: %w", err)
	}

	// 初始化默认数据
	err = seedData()
	if err != nil {
		return fmt.Errorf("初始化默认数据失败: %w", err)
	}

	return nil
}

// 自动迁移表结构
func migrateDB() error {
	log.Println("正在迁移表结构...")
	return DB.AutoMigrate(
		&models.User{},
		&models.Category{},
		&models.Transaction{},
	)
}

// 初始化默认数据
func seedData() error {
	log.Println("正在检查默认数据...")

	// 初始化默认分类
	return seedDefaultCategories()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// HealthCheck 数据库健康检查
func HealthCheck() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// 初始化默认分类
func seedDefaultCategories() error {
	// 检查是否已存在分类
	var count int64
	DB.Model(&models.Category{}).Count(&count)
	if count > 0 {
		log.Println("已存在分类数据，跳过初始化")
		return nil
	}

	// 默认收入分类
	incomeCategories := []models.Category{
		{Name: "工资", Type: "income", Icon: "salary", Color: "#4CAF50", IsDefault: true},
		{Name: "奖金", Type: "income", Icon: "bonus", Color: "#8BC34A", IsDefault: true},
		{Name: "投资", Type: "income", Icon: "investment", Color: "#CDDC39", IsDefault: true},
		{Name: "报销", Type: "income", Icon: "reimburse", Color: "#FFC107", IsDefault: true},
		{Name: "其他收入", Type: "income", Icon: "other_income", Color: "#FF9800", IsDefault: true},
	}

	// 默认支出分类
	expenseCategories := []models.Category{
		{Name: "餐饮", Type: "expense", Icon: "food", Color: "#F44336", IsDefault: true},
		{Name: "交通", Type: "expense", Icon: "transport", Color: "#E91E63", IsDefault: true},
		{Name: "购物", Type: "expense", Icon: "shopping", Color: "#9C27B0", IsDefault: true},
		{Name: "娱乐", Type: "expense", Icon: "entertainment", Color: "#673AB7", IsDefault: true},
		{Name: "居家", Type: "expense", Icon: "home", Color: "#3F51B5", IsDefault: true},
		{Name: "通讯", Type: "expense", Icon: "communication", Color: "#2196F3", IsDefault: true},
		{Name: "医疗", Type: "expense", Icon: "medical", Color: "#00BCD4", IsDefault: true},
		{Name: "教育", Type: "expense", Icon: "education", Color: "#009688", IsDefault: true},
		{Name: "其他支出", Type: "expense", Icon: "other_expense", Color: "#FF5722", IsDefault: true},
	}

	// 合并所有分类
	var allCategories []models.Category
	allCategories = append(allCategories, incomeCategories...)
	allCategories = append(allCategories, expenseCategories...)

	// 批量创建分类
	return DB.Create(&allCategories).Error
} 