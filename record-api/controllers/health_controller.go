package controllers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sakura/record-api/api"
	"github.com/sakura/record-api/database"
)

// HealthCheck 健康检查接口
func HealthCheck(c *gin.Context) {
	// 检查数据库连接
	dbStatus := "ok"
	if err := database.HealthCheck(); err != nil {
		dbStatus = "error: " + err.Error()
	}

	// 返回健康状态
	c.JSON(http.StatusOK, api.SuccessResponse(gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"services": gin.H{
			"database": dbStatus,
		},
		"version": "1.0.0",
	}))
}

// Ping 简单的ping接口
func Ping(c *gin.Context) {
	c.JSON(http.StatusOK, api.SuccessResponse(gin.H{
		"message": "pong",
		"timestamp": time.Now().Unix(),
	}))
}
