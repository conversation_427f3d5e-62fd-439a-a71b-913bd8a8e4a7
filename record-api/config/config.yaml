server:
  port: 8080
  mode: debug

database:
  # 替换为您的Supabase项目信息
  host: db.your-project-ref.supabase.co
  port: 5432
  user: postgres
  password: your-supabase-password
  dbname: postgres
  sslmode: require

jwt:
  secret: your-jwt-secret-key-change-this-in-production
  expiretime: 24

# Supabase配置说明：
# 1. host: 在Supabase Dashboard -> Settings -> Database -> Connection info 中找到
# 2. password: 您在创建Supabase项目时设置的数据库密码
# 3. dbname: Supabase默认使用 "postgres" 作为数据库名
# 4. sslmode: Supabase要求使用SSL连接，设置为 "require"
