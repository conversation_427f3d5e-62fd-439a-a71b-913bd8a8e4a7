package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sakura/record-api/api"
	"github.com/sakura/record-api/utils"
)

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从 HTTP 头中获取 token
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, api.Response{
				Code:    http.StatusUnauthorized,
				Message: "未提供认证令牌",
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 检查 Authorization 格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.JSON(http.StatusUnauthorized, api.Response{
				Code:    http.StatusUnauthorized,
				Message: "认证令牌格式错误",
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 解析 token
		token := parts[1]
		claims, err := utils.ParseToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, api.Response{
				Code:    http.StatusUnauthorized,
				Message: "无效的认证令牌: " + err.Error(),
				Data:    nil,
			})
			c.Abort()
			return
		}

		// 将用户信息保存到上下文
		c.Set("userId", claims.UserID)
		c.Next()
	}
}
